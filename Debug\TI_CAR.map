******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Fri Aug  1 09:23:37 2025

OUTPUT FILE NAME:   <TI_CAR.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000073a9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00009778  00016888  R  X
  SRAM                  20200000   00008000  000006b1  0000794f  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00009778   00009778    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00007f60   00007f60    r-x .text
  00008020    00008020    000016d0   000016d0    r-- .rodata
  000096f0    000096f0    00000088   00000088    r-- .cinit
20200000    20200000    000004b2   00000000    rw-
  20200000    20200000    00000323   00000000    rw- .bss
  20200324    20200324    0000018e   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00007f60     
                  000000c0    000009d0     libc.a : _printfi.c.obj (.text:__TI_printfi)
                  00000a90    00000364            : e_asin.c.obj (.text.asin)
                  00000df4    000002f8            : s_atan.c.obj (.text.atan)
                  000010ec    00000290     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  0000137c    00000278     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_feature)
                  000015f4    00000238     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_thresh)
                  0000182c    0000022c     MPU6050.o (.text.Read_Quad)
                  00001a58    0000022c     inv_mpu.o (.text.mpu_reset_fifo)
                  00001c84    00000220     libc.a : _printfi.c.obj (.text._pconv_a)
                  00001ea4    000001f4     inv_mpu_dmp_motion_driver.o (.text.dmp_read_fifo)
                  00002098    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00002274    000001b0     Task.o (.text.Task_Start)
                  00002424    000001a0     inv_mpu.o (.text.mpu_set_bypass)
                  000025c4    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  00002756    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002758    00000188     libc.a : e_atan2.c.obj (.text.atan2)
                  000028e0    00000178     inv_mpu_dmp_motion_driver.o (.text.dmp_set_orientation)
                  00002a58    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002bc8    00000168     Interrupt.o (.text.GROUP1_IRQHandler)
                  00002d30    00000144     MPU6050.o (.text.MPU6050_Init)
                  00002e74    0000013c     Tracker.o (.text.Tracker_Read)
                  00002fb0    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  000030ec    00000134     MPU6050.o (.text.mspm0_i2c_read)
                  00003220    00000134     libc.a : qsort.c.obj (.text.qsort)
                  00003354    00000130     OLED.o (.text.OLED_ShowChar)
                  00003484    00000130     inv_mpu.o (.text.mpu_set_sensors)
                  000035b4    00000128     inv_mpu.o (.text.mpu_init)
                  000036dc    00000124     PID_IQMath.o (.text.PID_IQ_Prosc)
                  00003800    00000124     inv_mpu.o (.text.mpu_load_firmware)
                  00003924    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00003a44    00000110     OLED.o (.text.OLED_Init)
                  00003b54    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003c60    00000108     inv_mpu.o (.text.mpu_read_fifo_stream)
                  00003d68    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00003e6c    00000104     Task_App.o (.text.Task_Motor_PID)
                  00003f70    00000100     inv_mpu.o (.text.mpu_lp_accel_mode)
                  00004070    000000ec     inv_mpu.o (.text.mpu_set_sample_rate)
                  0000415c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00004240    000000e4     inv_mpu.o (.text.mpu_set_accel_fsr)
                  00004324    000000dc     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_configSYSPLL)
                  00004400    000000dc     Task_App.o (.text.Task_OLED)
                  000044dc    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000045b4    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000468c    000000d4     inv_mpu.o (.text.set_int_enable)
                  00004760    000000d0     inv_mpu.o (.text.mpu_set_lpf)
                  00004830    000000c4     inv_mpu.o (.text.mpu_set_gyro_fsr)
                  000048f4    000000c4     MPU6050.o (.text.mspm0_i2c_write)
                  000049b8    000000bc     Motor.o (.text.Motor_Start)
                  00004a74    000000bc     inv_mpu.o (.text.mpu_configure_fifo)
                  00004b30    000000b8     inv_mpu.o (.text.mpu_set_dmp_state)
                  00004be8    000000b4     Task.o (.text.Task_Add)
                  00004c9c    000000b0     Task_App.o (.text.Task_Init)
                  00004d4c    000000ac     inv_mpu.o (.text.mpu_read_mem)
                  00004df8    000000ac     inv_mpu.o (.text.mpu_write_mem)
                  00004ea4    000000a4     Motor.o (.text.Motor_GetSpeed)
                  00004f48    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  00004fea    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00004fec    000000a0     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  0000508c    0000009c     Motor.o (.text.Motor_SetDuty)
                  00005128    0000009c     inv_mpu.o (.text.mpu_set_int_latched)
                  000051c4    00000098     OLED.o (.text.I2C_OLED_WR_Byte)
                  0000525c    00000098     inv_mpu_dmp_motion_driver.o (.text.dmp_set_fifo_rate)
                  000052f4    00000096     MPU6050.o (.text.inv_row_2_scale)
                  0000538a    00000002     --HOLE-- [fill = 0]
                  0000538c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorBack_init)
                  00005418    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_MotorFront_init)
                  000054a4    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00005530    0000008c     inv_mpu_dmp_motion_driver.o (.text.decode_gesture)
                  000055bc    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART0_init)
                  00005640    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  000056c4    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00005746    00000002     --HOLE-- [fill = 0]
                  00005748    00000080     Task_App.o (.text.Task_Serial)
                  000057c8    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00005844    00000074     Motor.o (.text.Motor_SetDirc)
                  000058b8    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  0000592c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00005930    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000059a4    00000074     inv_mpu.o (.text.mpu_get_accel_fsr)
                  00005a18    00000070     Serial.o (.text.MyPrintf_DMA)
                  00005a88    0000006e     OLED.o (.text.OLED_ShowString)
                  00005af6    0000006a     OLED.o (.text.I2C_OLED_Clear)
                  00005b60    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00005bc8    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00005c2e    00000066     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_axes)
                  00005c94    00000064     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
                  00005cf8    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00005d5c    00000064     MPU6050.o (.text.mpu6050_i2c_sda_unlock)
                  00005dc0    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005e22    00000002     --HOLE-- [fill = 0]
                  00005e24    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005e86    00000002     --HOLE-- [fill = 0]
                  00005e88    00000060     OLED.o (.text.I2C_OLED_i2c_sda_unlock)
                  00005ee8    00000060     Key_Led.o (.text.Key_Read)
                  00005f48    00000060     Task_App.o (.text.Task_IdleFunction)
                  00005fa8    00000060     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_gyro_cal)
                  00006008    00000060     inv_mpu.o (.text.mpu_get_gyro_fsr)
                  00006068    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000060c6    00000002     --HOLE-- [fill = 0]
                  000060c8    0000005c     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00006124    0000005c     Task_App.o (.text.Task_Tracker)
                  00006180    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000061dc    0000005c     MPU6050.o (.text.mspm0_i2c_enable)
                  00006238    0000005c     OLED.o (.text.mspm0_i2c_enable)
                  00006294    00000058     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_MPU6050_init)
                  000062ec    00000058     Serial.o (.text.Serial_Init)
                  00006344    00000058     libc.a : _ltoa.c.obj (.text.__TI_ltoa)
                  0000639c    00000058            : _printfi.c.obj (.text._pconv_f)
                  000063f4    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000644a    00000002     --HOLE-- [fill = 0]
                  0000644c    00000054     Interrupt.o (.text.Interrupt_Init)
                  000064a0    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000064f2    00000002     --HOLE-- [fill = 0]
                  000064f4    00000050     MPU6050.o (.text.DL_I2C_startControllerTransfer)
                  00006544    00000050     OLED.o (.text.DL_I2C_startControllerTransfer)
                  00006594    00000050     ti_msp_dl_config.o (.text.SysTick_Config)
                  000065e4    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  00006630    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  0000667c    0000004c     OLED.o (.text.OLED_Printf)
                  000066c8    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00006712    00000002     --HOLE-- [fill = 0]
                  00006714    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  0000675c    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_6x_lp_quat)
                  000067a4    00000048     inv_mpu_dmp_motion_driver.o (.text.dmp_enable_lp_quat)
                  000067ec    00000048     MPU6050.o (.text.mspm0_i2c_disable)
                  00006834    00000044     driverlib.a : dl_sysctl_mspm0g1x0x_g3x0x.o (.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
                  00006878    00000044     PID_IQMath.o (.text.PID_IQ_SetParams)
                  000068bc    00000044     Task_App.o (.text.Task_Key)
                  00006900    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_thresh)
                  00006944    00000044     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_count)
                  00006988    00000044     OLED.o (.text.mspm0_i2c_disable)
                  000069cc    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  00006a0e    00000002     --HOLE-- [fill = 0]
                  00006a10    00000040                            : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00006a50    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00006a90    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00006ad0    00000040            : vsnprintf.c.obj (.text.vsnprintf)
                  00006b10    0000003e     Task.o (.text.Task_CMP)
                  00006b4e    0000003e     MPU6050.o (.text.inv_orientation_matrix_to_scalar)
                  00006b8c    0000003c     MPU6050.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006bc8    0000003c     OLED.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006c04    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00006c40    0000003c     driverlib.a : dl_i2c.o (.text.DL_I2C_flushControllerTXFIFO)
                  00006c7c    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00006cb8    0000003c     OLED.o (.text.I2C_OLED_Set_Pos)
                  00006cf4    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00006d30    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00006d6c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00006da8    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00006de2    00000002     --HOLE-- [fill = 0]
                  00006de4    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00006e1e    00000002     --HOLE-- [fill = 0]
                  00006e20    00000038     Task_App.o (.text.Task_LED)
                  00006e58    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00006e90    00000034     MPU6050.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006ec4    00000034     OLED.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006ef8    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00006f2c    00000034     inv_mpu.o (.text.mpu_get_sample_rate)
                  00006f60    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_time)
                  00006f92    00000032     inv_mpu_dmp_motion_driver.o (.text.dmp_set_shake_reject_timeout)
                  00006fc4    00000030     Serial.o (.text.DL_DMA_setTransferSize)
                  00006ff4    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_RX_init)
                  00007024    00000030     iqmath.a : _IQNtoF.o (.text._IQ24toF)
                  00007054    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00007084    00000030            : vsnprintf.c.obj (.text._outs)
                  000070b4    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time)
                  000070e4    00000030     inv_mpu_dmp_motion_driver.o (.text.dmp_set_tap_time_multi)
                  00007114    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00007140    0000002c     Interrupt.o (.text.__NVIC_EnableIRQ)
                  0000716c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00007198    0000002c     libc.a : vsprintf.c.obj (.text.vsprintf)
                  000071c4    0000002a     PID_IQMath.o (.text.PID_IQ_Init)
                  000071ee    00000028     MPU6050.o (.text.DL_Common_updateReg)
                  00007216    00000028     OLED.o (.text.DL_Common_updateReg)
                  0000723e    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00007266    00000002     --HOLE-- [fill = 0]
                  00007268    00000028     Serial.o (.text.DL_DMA_setDestAddr)
                  00007290    00000028     Serial.o (.text.DL_DMA_setSrcAddr)
                  000072b8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  000072e0    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00007308    00000028     ti_msp_dl_config.o (.text.DL_UART_setRXFIFOThreshold)
                  00007330    00000028     ti_msp_dl_config.o (.text.DL_UART_setTXFIFOThreshold)
                  00007358    00000028     SysTick.o (.text.SysTick_Increasment)
                  00007380    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  000073a8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000073d0    00000026     Serial.o (.text.DL_DMA_disableChannel)
                  000073f6    00000026     Serial.o (.text.DL_DMA_enableChannel)
                  0000741c    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00007442    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00007468    00000024     ti_msp_dl_config.o (.text.DL_UART_setRXInterruptTimeout)
                  0000748c    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  000074b0    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  000074d2    00000002     --HOLE-- [fill = 0]
                  000074d4    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000074f4    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setFlashWaitState)
                  00007514    00000020     SysTick.o (.text.Delay)
                  00007534    00000020     main.o (.text.main)
                  00007554    00000020     libc.a : memcmp.c.obj (.text.memcmp)
                  00007574    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00007592    00000002     --HOLE-- [fill = 0]
                  00007594    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000075b2    00000002     --HOLE-- [fill = 0]
                  000075b4    0000001c     ti_msp_dl_config.o (.text.DL_DMA_enableInterrupt)
                  000075d0    0000001c     Interrupt.o (.text.DL_GPIO_clearInterruptStatus)
                  000075ec    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00007608    0000001c     MPU6050.o (.text.DL_GPIO_enableHiZ)
                  00007624    0000001c     OLED.o (.text.DL_GPIO_enableHiZ)
                  00007640    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  0000765c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00007678    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00007694    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  000076b0    0000001c     MPU6050.o (.text.DL_I2C_getSDAStatus)
                  000076cc    0000001c     OLED.o (.text.DL_I2C_getSDAStatus)
                  000076e8    0000001c     MPU6050.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00007704    0000001c     Interrupt.o (.text.DL_Interrupt_getPendingGroup)
                  00007720    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000773c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00007758    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00007774    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00007790    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000077ac    0000001c     inv_mpu_dmp_motion_driver.o (.text.dmp_load_motion_driver_firmware)
                  000077c8    00000018     ti_msp_dl_config.o (.text.DL_DMA_clearInterruptStatus)
                  000077e0    00000018     MPU6050.o (.text.DL_GPIO_enableOutput)
                  000077f8    00000018     OLED.o (.text.DL_GPIO_enableOutput)
                  00007810    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00007828    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00007840    00000018     Interrupt.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00007858    00000018     MPU6050.o (.text.DL_GPIO_initDigitalOutput)
                  00007870    00000018     OLED.o (.text.DL_GPIO_initDigitalOutput)
                  00007888    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000078a0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralAnalogFunction)
                  000078b8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000078d0    00000018     MPU6050.o (.text.DL_GPIO_setPins)
                  000078e8    00000018     Motor.o (.text.DL_GPIO_setPins)
                  00007900    00000018     OLED.o (.text.DL_GPIO_setPins)
                  00007918    00000018     Task_App.o (.text.DL_GPIO_setPins)
                  00007930    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00007948    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00007960    00000018     MPU6050.o (.text.DL_I2C_clearInterruptStatus)
                  00007978    00000018     OLED.o (.text.DL_I2C_clearInterruptStatus)
                  00007990    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000079a8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  000079c0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  000079d8    00000018     MPU6050.o (.text.DL_I2C_enablePower)
                  000079f0    00000018     OLED.o (.text.DL_I2C_enablePower)
                  00007a08    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00007a20    00000018     MPU6050.o (.text.DL_I2C_getRawInterruptStatus)
                  00007a38    00000018     OLED.o (.text.DL_I2C_getRawInterruptStatus)
                  00007a50    00000018     MPU6050.o (.text.DL_I2C_reset)
                  00007a68    00000018     OLED.o (.text.DL_I2C_reset)
                  00007a80    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00007a98    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00007ab0    00000018     ti_msp_dl_config.o (.text.DL_MathACL_enablePower)
                  00007ac8    00000018     ti_msp_dl_config.o (.text.DL_MathACL_reset)
                  00007ae0    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00007af8    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00007b10    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00007b28    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00007b40    00000018     Motor.o (.text.DL_Timer_startCounter)
                  00007b58    00000018     Interrupt.o (.text.DL_UART_clearInterruptStatus)
                  00007b70    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMAReceiveEvent)
                  00007b88    00000018     ti_msp_dl_config.o (.text.DL_UART_enableDMATransmitEvent)
                  00007ba0    00000018     ti_msp_dl_config.o (.text.DL_UART_enableFIFOs)
                  00007bb8    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00007bd0    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00007be8    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH_TX_init)
                  00007c00    00000018     iqmath.a : _IQNdiv.o (.text._IQ24div)
                  00007c18    00000018              : _IQNmpy.o (.text._IQ24mpy)
                  00007c30    00000018     libc.a : vsnprintf.c.obj (.text._outc)
                  00007c48    00000018            : vsprintf.c.obj (.text._outs)
                  00007c60    00000016     Interrupt.o (.text.DL_GPIO_readPins)
                  00007c76    00000016     Key_Led.o (.text.DL_GPIO_readPins)
                  00007c8c    00000016     MPU6050.o (.text.DL_GPIO_readPins)
                  00007ca2    00000016     OLED.o (.text.DL_GPIO_readPins)
                  00007cb8    00000016     Tracker.o (.text.DL_GPIO_readPins)
                  00007cce    00000016     MPU6050.o (.text.DL_I2C_transmitControllerData)
                  00007ce4    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00007cfa    00000016     SysTick.o (.text.SysGetTick)
                  00007d10    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00007d26    00000014     MPU6050.o (.text.DL_GPIO_clearPins)
                  00007d3a    00000014     Motor.o (.text.DL_GPIO_clearPins)
                  00007d4e    00000014     OLED.o (.text.DL_GPIO_clearPins)
                  00007d62    00000014     Task_App.o (.text.DL_GPIO_clearPins)
                  00007d76    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00007d8a    00000002     --HOLE-- [fill = 0]
                  00007d8c    00000014     MPU6050.o (.text.DL_I2C_getControllerStatus)
                  00007da0    00000014     OLED.o (.text.DL_I2C_getControllerStatus)
                  00007db4    00000014     MPU6050.o (.text.DL_I2C_receiveControllerData)
                  00007dc8    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00007ddc    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00007df0    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00007e04    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00007e18    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  00007e2c    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_android_orient_cb)
                  00007e40    00000014     inv_mpu_dmp_motion_driver.o (.text.dmp_register_tap_cb)
                  00007e54    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00007e68    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00007e7a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00007e8c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00007e9e    00000002     --HOLE-- [fill = 0]
                  00007ea0    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00007eb0    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00007ec0    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00007ed0    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00007ee0    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00007eee    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00007efc    0000000e     MPU6050.o (.text.tap_cb)
                  00007f0a    0000000e     libc.a : memset16.S.obj (.text:TI_memset_small)
                  00007f18    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00007f24    0000000c     SysTick.o (.text.Sys_GetTick)
                  00007f30    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00007f3a    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007f44    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00007f54    0000000a     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007f5e    00000002     --HOLE-- [fill = 0]
                  00007f60    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00007f70    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00007f7a    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007f84    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007f8e    0000000a            : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00007f98    00000010     libclang_rt.builtins.a : divdf3.S.obj (.tramp.__aeabi_ddiv.1)
                  00007fa8    0000000a     libc.a : vsprintf.c.obj (.text._outc)
                  00007fb2    0000000a     MPU6050.o (.text.android_orient_cb)
                  00007fbc    00000008     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007fc4    00000008     Interrupt.o (.text.SysTick_Handler)
                  00007fcc    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00007fd4    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00007fdc    00000006     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00007fe2    00000002     --HOLE-- [fill = 0]
                  00007fe4    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00007ff4    00000006     libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00007ffa    00000004            : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00007ffe    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00008002    00000002     --HOLE-- [fill = 0]
                  00008004    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00008014    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00008018    00000004            : exit.c.obj (.text:abort)
                  0000801c    00000004     --HOLE-- [fill = 0]

.cinit     0    000096f0    00000088     
                  000096f0    00000062     (.cinit..data.load) [load image, compression = lzss]
                  00009752    00000002     --HOLE-- [fill = 0]
                  00009754    0000000c     (__TI_handler_table)
                  00009760    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00009768    00000010     (__TI_cinit_table)

.rodata    0    00008020    000016d0     
                  00008020    00000bf6     inv_mpu_dmp_motion_driver.o (.rodata.dmp_memory)
                  00008c16    000005f0     OLED_Font.o (.rodata.asc2_1608)
                  00009206    00000228     OLED_Font.o (.rodata.asc2_0806)
                  0000942e    00000002     ti_msp_dl_config.o (.rodata.gI2C_MPU6050ClockConfig)
                  00009430    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00009531    00000007     Task_App.o (.rodata.str1.136405643080007560121)
                  00009538    00000040     libc.a : s_atan.c.obj (.rodata.cst32)
                  00009578    00000028     ti_msp_dl_config.o (.rodata.gSYSPLLConfig)
                  000095a0    00000028     inv_mpu.o (.rodata.test)
                  000095c8    0000001e     inv_mpu.o (.rodata.reg)
                  000095e6    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000095e8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_RXConfig)
                  00009600    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH_TXConfig)
                  00009618    00000014     Task_App.o (.rodata.str1.157702741485139367601)
                  0000962c    00000014     Task_App.o (.rodata.str1.182657883079055368591)
                  00009640    00000014     Task_App.o (.rodata.str1.97872905622636903301)
                  00009654    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  00009665    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  00009676    00000011     Task_App.o (.rodata.str1.183384535776591351011)
                  00009687    00000011     Task_App.o (.rodata.str1.25142174965186748781)
                  00009698    0000000c     inv_mpu.o (.rodata.hw)
                  000096a4    0000000a     ti_msp_dl_config.o (.rodata.gUART0Config)
                  000096ae    00000002     ti_msp_dl_config.o (.rodata.gUART0ClockConfig)
                  000096b0    00000008     ti_msp_dl_config.o (.rodata.gMotorBackConfig)
                  000096b8    00000008     ti_msp_dl_config.o (.rodata.gMotorFrontConfig)
                  000096c0    00000008     Task_App.o (.rodata.str1.67400646179352630301)
                  000096c8    00000006     Task_App.o (.rodata.str1.115332825834609149281)
                  000096ce    00000005     Task_App.o (.rodata.str1.87978995337490384161)
                  000096d3    00000004     Task_App.o (.rodata.str1.134609064190095881641)
                  000096d7    00000004     Task_App.o (.rodata.str1.171900814140190138471)
                  000096db    00000003     ti_msp_dl_config.o (.rodata.gMotorBackClockConfig)
                  000096de    00000003     ti_msp_dl_config.o (.rodata.gMotorFrontClockConfig)
                  000096e1    0000000f     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000323     UNINITIALIZED
                  20200000    00000200     (.common:Serial_RxData)
                  20200200    000000f0     Task.o (.bss.Task_Schedule)
                  202002f0    00000010     (.common:quat)
                  20200300    00000006     (.common:Data_Accel)
                  20200306    00000006     (.common:Data_Gyro)
                  2020030c    00000004     (.common:Data_Pitch)
                  20200310    00000004     (.common:Data_Roll)
                  20200314    00000004     (.common:Data_Yaw)
                  20200318    00000004     (.common:ExISR_Flag)
                  2020031c    00000004     (.common:sensor_timestamp)
                  20200320    00000002     (.common:sensors)
                  20200322    00000001     (.common:more)

.data      0    20200324    0000018e     UNINITIALIZED
                  20200324    00000040     Motor.o (.data.Motor_Back_Left)
                  20200364    00000040     Motor.o (.data.Motor_Back_Right)
                  202003a4    00000040     Motor.o (.data.Motor_Font_Left)
                  202003e4    00000040     Motor.o (.data.Motor_Font_Right)
                  20200424    0000002c     inv_mpu.o (.data.st)
                  20200450    00000010     Task_App.o (.data.Motor)
                  20200460    00000010     inv_mpu_dmp_motion_driver.o (.data.dmp)
                  20200470    0000000e     MPU6050.o (.data.hal)
                  2020047e    00000009     MPU6050.o (.data.gyro_orientation)
                  20200487    00000001     Task_App.o (.data.Flag_LED)
                  20200488    00000008     Task_App.o (.data.Data_MotorEncoder)
                  20200490    00000008     Task_App.o (.data.Data_Tracker_Input)
                  20200498    00000004     Task_App.o (.data.Data_Motor_TarSpeed)
                  2020049c    00000004     Task_App.o (.data.Data_Tracker_Offset)
                  202004a0    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202004a4    00000004     SysTick.o (.data.delayTick)
                  202004a8    00000004     SysTick.o (.data.uwTick)
                  202004ac    00000002     Task_App.o (.data.Task_IdleFunction.CNT)
                  202004ae    00000001     Interrupt.o (.data.Flag_MPU6050_Ready)
                  202004af    00000001     Task_App.o (.data.Task_Key.Key_Old)
                  202004b0    00000001     Task.o (.data.Task_Num)
                  202004b1    00000001     Interrupt.o (.data.enable_group1_irq)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3446    126       0      
       startup_mspm0g350x_ticlang.o   8       192       0      
       main.o                         32      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3486    318       0      
                                                               
    .\APP\Src\
       Task_App.o                     1140    128       44     
       Interrupt.o                    622     0         6      
    +--+------------------------------+-------+---------+---------+
       Total:                         1762    128       50     
                                                               
    .\BSP\Src\
       MPU6050.o                      2472    0         70     
       OLED_Font.o                    0       2072      0      
       OLED.o                         1854    0         0      
       Motor.o                        692     0         256    
       Serial.o                       404     0         512    
       Task.o                         674     0         241    
       PID_IQMath.o                   402     0         0      
       Tracker.o                      338     0         0      
       Key_Led.o                      118     0         0      
       SysTick.o                      106     0         8      
    +--+------------------------------+-------+---------+---------+
       Total:                         7060    2072      1087   
                                                               
    .\DMP\
       inv_mpu_dmp_motion_driver.o    3110    3062      16     
       inv_mpu.o                      4600    82        44     
    +--+------------------------------+-------+---------+---------+
       Total:                         7710    3144      60     
                                                               
    D:/ti/ccstheia151/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_sysctl_mspm0g1x0x_g3x0x.o   388     0         0      
       dl_timer.o                     356     0         0      
       dl_i2c.o                       192     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1112    0         0      
                                                               
    D:/ti/ccstheia151/mspm0_sdk_2_05_01_00/source/ti/iqmath/lib/ticlang/m0p/mathacl/mspm0g1x0x_g3x0x/iqmath.a
       _IQNtoF.o                      48      0         0      
       _IQNdiv.o                      24      0         0      
       _IQNmpy.o                      24      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         96      0         0      
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_asin.c.obj                   908     0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  392     0         0      
       e_sqrt.c.obj                   368     0         0      
       qsort.c.obj                    308     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       vsnprintf.c.obj                136     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       _ltoa.c.obj                    88      0         0      
       vsprintf.c.obj                 78      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       memcmp.c.obj                   32      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         8354    355       4      
                                                               
    D:\ti\ccstheia151\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   284     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsisf.S.obj              40      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memset.S.obj             14      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2984    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       134       0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   32564   6151      1713   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00009768 records: 2, size/record: 8, table size: 16
	.data: load addr=000096f0, load size=00000062 bytes, run addr=20200324, run size=0000018e bytes, compression=lzss
	.bss: load addr=00009760, load size=00000008 bytes, run addr=20200000, run size=00000323 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00009754 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000025c5     00007f44     00007f42   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   0000415d     00007f60     00007f5c   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007f78          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00007f8c          : e_asin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007fc2          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00007ff8          : e_asin.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_ddiv              $Tramp$TT$L$PI$$__aeabi_ddiv
   00003b55     00007f98     00007f96   libc.a : e_asin.c.obj (.text.OUTLINED_FUNCTION_3)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   000025cf     00007fe4     00007fe0   libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00007ffc          : e_asin.c.obj (.text.OUTLINED_FUNCTION_4)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000073a9     00008004     00007ffe   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[10 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                                 
-------   ----                                 
0000592d  ADC0_IRQHandler                      
0000592d  ADC1_IRQHandler                      
0000592d  AES_IRQHandler                       
00008018  C$$EXIT                              
0000592d  CANFD0_IRQHandler                    
0000592d  DAC0_IRQHandler                      
00007f31  DL_Common_delayCycles                
000065e5  DL_DMA_initChannel                   
00006069  DL_I2C_fillControllerTXFIFO          
00006c41  DL_I2C_flushControllerTXFIFO         
00007443  DL_I2C_setClockConfig                
00004325  DL_SYSCTL_configSYSPLL               
00005c95  DL_SYSCTL_setHFCLKSourceHFXTParams   
00006835  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00003d69  DL_Timer_initFourCCPWMMode           
00007759  DL_Timer_setCaptCompUpdateMethod     
00007b29  DL_Timer_setCaptureCompareOutCtl     
00007eb1  DL_Timer_setCaptureCompareValue      
00007775  DL_Timer_setClockConfig              
00006715  DL_UART_init                         
00007e69  DL_UART_setClockConfig               
0000592d  DMA_IRQHandler                       
20200300  Data_Accel                           
20200306  Data_Gyro                            
20200488  Data_MotorEncoder                    
20200498  Data_Motor_TarSpeed                  
2020030c  Data_Pitch                           
20200310  Data_Roll                            
20200490  Data_Tracker_Input                   
2020049c  Data_Tracker_Offset                  
20200314  Data_Yaw                             
0000592d  Default_Handler                      
00007515  Delay                                
20200318  ExISR_Flag                           
20200487  Flag_LED                             
202004ae  Flag_MPU6050_Ready                   
0000592d  GROUP0_IRQHandler                    
00002bc9  GROUP1_IRQHandler                    
0000592d  HardFault_Handler                    
0000592d  I2C0_IRQHandler                      
0000592d  I2C1_IRQHandler                      
00005af7  I2C_OLED_Clear                       
00006cb9  I2C_OLED_Set_Pos                     
000051c5  I2C_OLED_WR_Byte                     
00005e89  I2C_OLED_i2c_sda_unlock              
0000644d  Interrupt_Init                       
00005ee9  Key_Read                             
00002d31  MPU6050_Init                         
20200450  Motor                                
20200324  Motor_Back_Left                      
20200364  Motor_Back_Right                     
202003a4  Motor_Font_Left                      
202003e4  Motor_Font_Right                     
00004ea5  Motor_GetSpeed                       
0000508d  Motor_SetDuty                        
000049b9  Motor_Start                          
00005a19  MyPrintf_DMA                         
0000592d  NMI_Handler                          
00003a45  OLED_Init                            
0000667d  OLED_Printf                          
00003355  OLED_ShowChar                        
00005a89  OLED_ShowString                      
000071c5  PID_IQ_Init                          
000036dd  PID_IQ_Prosc                         
00006879  PID_IQ_SetParams                     
0000592d  PendSV_Handler                       
0000592d  RTC_IRQHandler                       
0000182d  Read_Quad                            
00007fff  Reset_Handler                        
0000592d  SPI0_IRQHandler                      
0000592d  SPI1_IRQHandler                      
0000592d  SVC_Handler                          
00006ff5  SYSCFG_DL_DMA_CH_RX_init             
00007be9  SYSCFG_DL_DMA_CH_TX_init             
00007f19  SYSCFG_DL_DMA_init                   
000010ed  SYSCFG_DL_GPIO_init                  
00006295  SYSCFG_DL_I2C_MPU6050_init           
00005cf9  SYSCFG_DL_I2C_OLED_init              
0000538d  SYSCFG_DL_MotorBack_init             
00005419  SYSCFG_DL_MotorFront_init            
000060c9  SYSCFG_DL_SYSCTL_init                
00007ec1  SYSCFG_DL_SYSTICK_init               
000055bd  SYSCFG_DL_UART0_init                 
00007115  SYSCFG_DL_init                       
00004fed  SYSCFG_DL_initPower                  
000062ed  Serial_Init                          
20200000  Serial_RxData                        
00007cfb  SysGetTick                           
00007fc5  SysTick_Handler                      
00007359  SysTick_Increasment                  
00007f25  Sys_GetTick                          
0000592d  TIMA0_IRQHandler                     
0000592d  TIMA1_IRQHandler                     
0000592d  TIMG0_IRQHandler                     
0000592d  TIMG12_IRQHandler                    
0000592d  TIMG6_IRQHandler                     
0000592d  TIMG7_IRQHandler                     
0000592d  TIMG8_IRQHandler                     
00007e7b  TI_memcpy_small                      
00007f0b  TI_memset_small                      
00004be9  Task_Add                             
00005f49  Task_IdleFunction                    
00004c9d  Task_Init                            
000068bd  Task_Key                             
00006e21  Task_LED                             
00003e6d  Task_Motor_PID                       
00004401  Task_OLED                            
00005749  Task_Serial                          
00002275  Task_Start                           
00006125  Task_Tracker                         
00002e75  Tracker_Read                         
0000592d  UART0_IRQHandler                     
0000592d  UART1_IRQHandler                     
0000592d  UART2_IRQHandler                     
0000592d  UART3_IRQHandler                     
00007c01  _IQ24div                             
00007c19  _IQ24mpy                             
00007025  _IQ24toF                             
20208000  __STACK_END                          
00000200  __STACK_SIZE                         
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00009768  __TI_CINIT_Base                      
00009778  __TI_CINIT_Limit                     
00009778  __TI_CINIT_Warm                      
00009754  __TI_Handler_Table_Base              
00009760  __TI_Handler_Table_Limit             
00006d6d  __TI_auto_init_nobinit_nopinit       
000057c9  __TI_decompress_lzss                 
00007e8d  __TI_decompress_none                 
00006345  __TI_ltoa                            
ffffffff  __TI_pprof_out_hndl                  
000000c1  __TI_printfi                         
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
00000000  __TI_static_base__                   
00007d11  __TI_zero_init_nomemset              
000025cf  __adddf3                             
000045bf  __addsf3                             
00009430  __aeabi_ctype_table_                 
00009430  __aeabi_ctype_table_C                
00005931  __aeabi_d2f                          
000066c9  __aeabi_d2iz                         
000069cd  __aeabi_d2uiz                        
000025cf  __aeabi_dadd                         
00005dc1  __aeabi_dcmpeq                       
00005dfd  __aeabi_dcmpge                       
00005e11  __aeabi_dcmpgt                       
00005de9  __aeabi_dcmple                       
00005dd5  __aeabi_dcmplt                       
00003b55  __aeabi_ddiv                         
0000415d  __aeabi_dmul                         
000025c5  __aeabi_dsub                         
202004a0  __aeabi_errno                        
00007fcd  __aeabi_errno_addr                   
00006a51  __aeabi_f2d                          
00006e59  __aeabi_f2iz                         
000045bf  __aeabi_fadd                         
00005e25  __aeabi_fcmpeq                       
00005e61  __aeabi_fcmpge                       
00005e75  __aeabi_fcmpgt                       
00005e4d  __aeabi_fcmple                       
00005e39  __aeabi_fcmplt                       
000056c5  __aeabi_fdiv                         
000054a5  __aeabi_fmul                         
000045b5  __aeabi_fsub                         
0000716d  __aeabi_i2d                          
00006cf5  __aeabi_i2f                          
000063f5  __aeabi_idiv                         
00002757  __aeabi_idiv0                        
000063f5  __aeabi_idivmod                      
00004feb  __aeabi_ldiv0                        
00007595  __aeabi_llsl                         
0000748d  __aeabi_lmul                         
00007fd5  __aeabi_memcpy                       
00007fd5  __aeabi_memcpy4                      
00007fd5  __aeabi_memcpy8                      
00007ee1  __aeabi_memset                       
00007ee1  __aeabi_memset4                      
00007ee1  __aeabi_memset8                      
00007381  __aeabi_ui2f                         
00006a11  __aeabi_uidiv                        
00006a11  __aeabi_uidivmod                     
00007e19  __aeabi_uldivmod                     
00007595  __ashldi3                            
ffffffff  __binit__                            
00005b61  __cmpdf2                             
00006da9  __cmpsf2                             
00003b55  __divdf3                             
000056c5  __divsf3                             
00005b61  __eqdf2                              
00006da9  __eqsf2                              
00006a51  __extendsfdf2                        
000066c9  __fixdfsi                            
00006e59  __fixsfsi                            
000069cd  __fixunsdfsi                         
0000716d  __floatsidf                          
00006cf5  __floatsisf                          
00007381  __floatunsisf                        
000058b9  __gedf2                              
00006d31  __gesf2                              
000058b9  __gtdf2                              
00006d31  __gtsf2                              
00005b61  __ledf2                              
00006da9  __lesf2                              
00005b61  __ltdf2                              
00006da9  __ltsf2                              
UNDEFED   __mpu_init                           
0000415d  __muldf3                             
0000748d  __muldi3                             
00006de5  __muldsi3                            
000054a5  __mulsf3                             
00005b61  __nedf2                              
00006da9  __nesf2                              
20207e00  __stack                              
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
000025c5  __subdf3                             
000045b5  __subsf3                             
00005931  __truncdfsf2                         
00004f49  __udivmoddi4                         
000073a9  _c_int00_noargs                      
UNDEFED   _system_post_cinit                   
00008015  _system_pre_init                     
00008019  abort                                
00009206  asc2_0806                            
00008c16  asc2_1608                            
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00002759  atan2                                
00002759  atan2l                               
00000df5  atanl                                
00006a91  atoi                                 
ffffffff  binit                                
202004a4  delayTick                            
0000675d  dmp_enable_6x_lp_quat                
0000137d  dmp_enable_feature                   
00005fa9  dmp_enable_gyro_cal                  
000067a5  dmp_enable_lp_quat                   
000077ad  dmp_load_motion_driver_firmware      
00001ea5  dmp_read_fifo                        
00007e2d  dmp_register_android_orient_cb       
00007e41  dmp_register_tap_cb                  
0000525d  dmp_set_fifo_rate                    
000028e1  dmp_set_orientation                  
00006901  dmp_set_shake_reject_thresh          
00006f61  dmp_set_shake_reject_time            
00006f93  dmp_set_shake_reject_timeout         
00005c2f  dmp_set_tap_axes                     
00006945  dmp_set_tap_count                    
000015f5  dmp_set_tap_thresh                   
000070b5  dmp_set_tap_time                     
000070e5  dmp_set_tap_time_multi               
202004b1  enable_group1_irq                    
00006181  frexp                                
00006181  frexpl                               
00009698  hw                                   
00000000  interruptVectors                     
000044dd  ldexp                                
000044dd  ldexpl                               
00007535  main                                 
000074b1  memccpy                              
00007555  memcmp                               
20200322  more                                 
00005d5d  mpu6050_i2c_sda_unlock               
00004a75  mpu_configure_fifo                   
000059a5  mpu_get_accel_fsr                    
00006009  mpu_get_gyro_fsr                     
00006f2d  mpu_get_sample_rate                  
000035b5  mpu_init                             
00003801  mpu_load_firmware                    
00003f71  mpu_lp_accel_mode                    
00003c61  mpu_read_fifo_stream                 
00004d4d  mpu_read_mem                         
00001a59  mpu_reset_fifo                       
00004241  mpu_set_accel_fsr                    
00002425  mpu_set_bypass                       
00004b31  mpu_set_dmp_state                    
00004831  mpu_set_gyro_fsr                     
00005129  mpu_set_int_latched                  
00004761  mpu_set_lpf                          
00004071  mpu_set_sample_rate                  
00003485  mpu_set_sensors                      
00004df9  mpu_write_mem                        
000030ed  mspm0_i2c_read                       
000048f5  mspm0_i2c_write                      
00003221  qsort                                
202002f0  quat                                 
000095c8  reg                                  
000044dd  scalbn                               
000044dd  scalbnl                              
2020031c  sensor_timestamp                     
20200320  sensors                              
00002a59  sqrt                                 
00002a59  sqrtl                                
000095a0  test                                 
202004a8  uwTick                               
00006ad1  vsnprintf                            
00007199  vsprintf                             
00007ed1  wcslen                               


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                                 
-------   ----                                 
00000000  __TI_ATRegion0_region_sz             
00000000  __TI_ATRegion0_src_addr              
00000000  __TI_ATRegion0_trg_addr              
00000000  __TI_ATRegion1_region_sz             
00000000  __TI_ATRegion1_src_addr              
00000000  __TI_ATRegion1_trg_addr              
00000000  __TI_ATRegion2_region_sz             
00000000  __TI_ATRegion2_src_addr              
00000000  __TI_ATRegion2_trg_addr              
00000000  __TI_static_base__                   
00000000  interruptVectors                     
000000c1  __TI_printfi                         
00000200  __STACK_SIZE                         
00000a91  asin                                 
00000a91  asinl                                
00000df5  atan                                 
00000df5  atanl                                
000010ed  SYSCFG_DL_GPIO_init                  
0000137d  dmp_enable_feature                   
000015f5  dmp_set_tap_thresh                   
0000182d  Read_Quad                            
00001a59  mpu_reset_fifo                       
00001ea5  dmp_read_fifo                        
00002275  Task_Start                           
00002425  mpu_set_bypass                       
000025c5  __aeabi_dsub                         
000025c5  __subdf3                             
000025cf  __adddf3                             
000025cf  __aeabi_dadd                         
00002757  __aeabi_idiv0                        
00002759  atan2                                
00002759  atan2l                               
000028e1  dmp_set_orientation                  
00002a59  sqrt                                 
00002a59  sqrtl                                
00002bc9  GROUP1_IRQHandler                    
00002d31  MPU6050_Init                         
00002e75  Tracker_Read                         
000030ed  mspm0_i2c_read                       
00003221  qsort                                
00003355  OLED_ShowChar                        
00003485  mpu_set_sensors                      
000035b5  mpu_init                             
000036dd  PID_IQ_Prosc                         
00003801  mpu_load_firmware                    
00003a45  OLED_Init                            
00003b55  __aeabi_ddiv                         
00003b55  __divdf3                             
00003c61  mpu_read_fifo_stream                 
00003d69  DL_Timer_initFourCCPWMMode           
00003e6d  Task_Motor_PID                       
00003f71  mpu_lp_accel_mode                    
00004071  mpu_set_sample_rate                  
0000415d  __aeabi_dmul                         
0000415d  __muldf3                             
00004241  mpu_set_accel_fsr                    
00004325  DL_SYSCTL_configSYSPLL               
00004401  Task_OLED                            
000044dd  ldexp                                
000044dd  ldexpl                               
000044dd  scalbn                               
000044dd  scalbnl                              
000045b5  __aeabi_fsub                         
000045b5  __subsf3                             
000045bf  __addsf3                             
000045bf  __aeabi_fadd                         
00004761  mpu_set_lpf                          
00004831  mpu_set_gyro_fsr                     
000048f5  mspm0_i2c_write                      
000049b9  Motor_Start                          
00004a75  mpu_configure_fifo                   
00004b31  mpu_set_dmp_state                    
00004be9  Task_Add                             
00004c9d  Task_Init                            
00004d4d  mpu_read_mem                         
00004df9  mpu_write_mem                        
00004ea5  Motor_GetSpeed                       
00004f49  __udivmoddi4                         
00004feb  __aeabi_ldiv0                        
00004fed  SYSCFG_DL_initPower                  
0000508d  Motor_SetDuty                        
00005129  mpu_set_int_latched                  
000051c5  I2C_OLED_WR_Byte                     
0000525d  dmp_set_fifo_rate                    
0000538d  SYSCFG_DL_MotorBack_init             
00005419  SYSCFG_DL_MotorFront_init            
000054a5  __aeabi_fmul                         
000054a5  __mulsf3                             
000055bd  SYSCFG_DL_UART0_init                 
000056c5  __aeabi_fdiv                         
000056c5  __divsf3                             
00005749  Task_Serial                          
000057c9  __TI_decompress_lzss                 
000058b9  __gedf2                              
000058b9  __gtdf2                              
0000592d  ADC0_IRQHandler                      
0000592d  ADC1_IRQHandler                      
0000592d  AES_IRQHandler                       
0000592d  CANFD0_IRQHandler                    
0000592d  DAC0_IRQHandler                      
0000592d  DMA_IRQHandler                       
0000592d  Default_Handler                      
0000592d  GROUP0_IRQHandler                    
0000592d  HardFault_Handler                    
0000592d  I2C0_IRQHandler                      
0000592d  I2C1_IRQHandler                      
0000592d  NMI_Handler                          
0000592d  PendSV_Handler                       
0000592d  RTC_IRQHandler                       
0000592d  SPI0_IRQHandler                      
0000592d  SPI1_IRQHandler                      
0000592d  SVC_Handler                          
0000592d  TIMA0_IRQHandler                     
0000592d  TIMA1_IRQHandler                     
0000592d  TIMG0_IRQHandler                     
0000592d  TIMG12_IRQHandler                    
0000592d  TIMG6_IRQHandler                     
0000592d  TIMG7_IRQHandler                     
0000592d  TIMG8_IRQHandler                     
0000592d  UART0_IRQHandler                     
0000592d  UART1_IRQHandler                     
0000592d  UART2_IRQHandler                     
0000592d  UART3_IRQHandler                     
00005931  __aeabi_d2f                          
00005931  __truncdfsf2                         
000059a5  mpu_get_accel_fsr                    
00005a19  MyPrintf_DMA                         
00005a89  OLED_ShowString                      
00005af7  I2C_OLED_Clear                       
00005b61  __cmpdf2                             
00005b61  __eqdf2                              
00005b61  __ledf2                              
00005b61  __ltdf2                              
00005b61  __nedf2                              
00005c2f  dmp_set_tap_axes                     
00005c95  DL_SYSCTL_setHFCLKSourceHFXTParams   
00005cf9  SYSCFG_DL_I2C_OLED_init              
00005d5d  mpu6050_i2c_sda_unlock               
00005dc1  __aeabi_dcmpeq                       
00005dd5  __aeabi_dcmplt                       
00005de9  __aeabi_dcmple                       
00005dfd  __aeabi_dcmpge                       
00005e11  __aeabi_dcmpgt                       
00005e25  __aeabi_fcmpeq                       
00005e39  __aeabi_fcmplt                       
00005e4d  __aeabi_fcmple                       
00005e61  __aeabi_fcmpge                       
00005e75  __aeabi_fcmpgt                       
00005e89  I2C_OLED_i2c_sda_unlock              
00005ee9  Key_Read                             
00005f49  Task_IdleFunction                    
00005fa9  dmp_enable_gyro_cal                  
00006009  mpu_get_gyro_fsr                     
00006069  DL_I2C_fillControllerTXFIFO          
000060c9  SYSCFG_DL_SYSCTL_init                
00006125  Task_Tracker                         
00006181  frexp                                
00006181  frexpl                               
00006295  SYSCFG_DL_I2C_MPU6050_init           
000062ed  Serial_Init                          
00006345  __TI_ltoa                            
000063f5  __aeabi_idiv                         
000063f5  __aeabi_idivmod                      
0000644d  Interrupt_Init                       
000065e5  DL_DMA_initChannel                   
0000667d  OLED_Printf                          
000066c9  __aeabi_d2iz                         
000066c9  __fixdfsi                            
00006715  DL_UART_init                         
0000675d  dmp_enable_6x_lp_quat                
000067a5  dmp_enable_lp_quat                   
00006835  DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
00006879  PID_IQ_SetParams                     
000068bd  Task_Key                             
00006901  dmp_set_shake_reject_thresh          
00006945  dmp_set_tap_count                    
000069cd  __aeabi_d2uiz                        
000069cd  __fixunsdfsi                         
00006a11  __aeabi_uidiv                        
00006a11  __aeabi_uidivmod                     
00006a51  __aeabi_f2d                          
00006a51  __extendsfdf2                        
00006a91  atoi                                 
00006ad1  vsnprintf                            
00006c41  DL_I2C_flushControllerTXFIFO         
00006cb9  I2C_OLED_Set_Pos                     
00006cf5  __aeabi_i2f                          
00006cf5  __floatsisf                          
00006d31  __gesf2                              
00006d31  __gtsf2                              
00006d6d  __TI_auto_init_nobinit_nopinit       
00006da9  __cmpsf2                             
00006da9  __eqsf2                              
00006da9  __lesf2                              
00006da9  __ltsf2                              
00006da9  __nesf2                              
00006de5  __muldsi3                            
00006e21  Task_LED                             
00006e59  __aeabi_f2iz                         
00006e59  __fixsfsi                            
00006f2d  mpu_get_sample_rate                  
00006f61  dmp_set_shake_reject_time            
00006f93  dmp_set_shake_reject_timeout         
00006ff5  SYSCFG_DL_DMA_CH_RX_init             
00007025  _IQ24toF                             
000070b5  dmp_set_tap_time                     
000070e5  dmp_set_tap_time_multi               
00007115  SYSCFG_DL_init                       
0000716d  __aeabi_i2d                          
0000716d  __floatsidf                          
00007199  vsprintf                             
000071c5  PID_IQ_Init                          
00007359  SysTick_Increasment                  
00007381  __aeabi_ui2f                         
00007381  __floatunsisf                        
000073a9  _c_int00_noargs                      
00007443  DL_I2C_setClockConfig                
0000748d  __aeabi_lmul                         
0000748d  __muldi3                             
000074b1  memccpy                              
00007515  Delay                                
00007535  main                                 
00007555  memcmp                               
00007595  __aeabi_llsl                         
00007595  __ashldi3                            
00007759  DL_Timer_setCaptCompUpdateMethod     
00007775  DL_Timer_setClockConfig              
000077ad  dmp_load_motion_driver_firmware      
00007b29  DL_Timer_setCaptureCompareOutCtl     
00007be9  SYSCFG_DL_DMA_CH_TX_init             
00007c01  _IQ24div                             
00007c19  _IQ24mpy                             
00007cfb  SysGetTick                           
00007d11  __TI_zero_init_nomemset              
00007e19  __aeabi_uldivmod                     
00007e2d  dmp_register_android_orient_cb       
00007e41  dmp_register_tap_cb                  
00007e69  DL_UART_setClockConfig               
00007e7b  TI_memcpy_small                      
00007e8d  __TI_decompress_none                 
00007eb1  DL_Timer_setCaptureCompareValue      
00007ec1  SYSCFG_DL_SYSTICK_init               
00007ed1  wcslen                               
00007ee1  __aeabi_memset                       
00007ee1  __aeabi_memset4                      
00007ee1  __aeabi_memset8                      
00007f0b  TI_memset_small                      
00007f19  SYSCFG_DL_DMA_init                   
00007f25  Sys_GetTick                          
00007f31  DL_Common_delayCycles                
00007fc5  SysTick_Handler                      
00007fcd  __aeabi_errno_addr                   
00007fd5  __aeabi_memcpy                       
00007fd5  __aeabi_memcpy4                      
00007fd5  __aeabi_memcpy8                      
00007fff  Reset_Handler                        
00008015  _system_pre_init                     
00008018  C$$EXIT                              
00008019  abort                                
00008c16  asc2_1608                            
00009206  asc2_0806                            
00009430  __aeabi_ctype_table_                 
00009430  __aeabi_ctype_table_C                
000095a0  test                                 
000095c8  reg                                  
00009698  hw                                   
00009754  __TI_Handler_Table_Base              
00009760  __TI_Handler_Table_Limit             
00009768  __TI_CINIT_Base                      
00009778  __TI_CINIT_Limit                     
00009778  __TI_CINIT_Warm                      
20200000  Serial_RxData                        
20200000  __start___llvm_prf_bits              
20200000  __start___llvm_prf_cnts              
20200000  __stop___llvm_prf_bits               
20200000  __stop___llvm_prf_cnts               
202002f0  quat                                 
20200300  Data_Accel                           
20200306  Data_Gyro                            
2020030c  Data_Pitch                           
20200310  Data_Roll                            
20200314  Data_Yaw                             
20200318  ExISR_Flag                           
2020031c  sensor_timestamp                     
20200320  sensors                              
20200322  more                                 
20200324  Motor_Back_Left                      
20200364  Motor_Back_Right                     
202003a4  Motor_Font_Left                      
202003e4  Motor_Font_Right                     
20200450  Motor                                
20200487  Flag_LED                             
20200488  Data_MotorEncoder                    
20200490  Data_Tracker_Input                   
20200498  Data_Motor_TarSpeed                  
2020049c  Data_Tracker_Offset                  
202004a0  __aeabi_errno                        
202004a4  delayTick                            
202004a8  uwTick                               
202004ae  Flag_MPU6050_Ready                   
202004b1  enable_group1_irq                    
20207e00  __stack                              
20208000  __STACK_END                          
ffffffff  __TI_pprof_out_hndl                  
ffffffff  __TI_prof_data_size                  
ffffffff  __TI_prof_data_start                 
ffffffff  __binit__                            
ffffffff  binit                                
UNDEFED   __mpu_init                           
UNDEFED   _system_post_cinit                   

[310 symbols]
