################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
APP/Src/%.o: ../APP/Src/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/code/TI_CAR" -I"C:/code/TI_CAR/APP/Inc" -I"C:/code/TI_CAR/BSP/Inc" -I"C:/code/TI_CAR/Debug" -I"D:/ti/CCS-SDK/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include" -I"D:/ti/CCS-SDK/mspm0_sdk_2_05_01_00/source" -I"C:/code/TI_CAR/DMP/Inc" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -MMD -MP -MF"APP/Src/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


