/*
 * MSPM0G3507 GEL脚本 - CCS Theia兼容版本
 * 修复了CCS Theia兼容性问题
 * 版本: v1.1 (CCS Theia Compatible)
 * 日期: 2025-07-30
 */

// 全局变量定义
int enableWaitForDebug = 0;  // 替代GEL_GetBoolDebugProperty

/*
 * OnPreTargetConnect() - 目标连接前的初始化
 * 修复: 移除不兼容的GEL_GetBoolDebugProperty调用
 */
OnPreTargetConnect()
{
    GEL_TextOut("MSPM0G3507: 开始目标连接前初始化...\n");
    
    // 原版代码 (CCS Theia不兼容):
    // enableWaitForDebug = GEL_GetBoolDebugProperty("EnableWaitForDebug");
    
    // 修复版本 (CCS Theia兼容):
    enableWaitForDebug = 0;  // 默认禁用等待调试
    
    GEL_TextOut("MSPM0G3507: 目标连接前初始化完成\n");
}

/*
 * OnPreFileLoaded() - 文件加载前的处理
 * 修复: 移除不兼容的调试属性调用
 */
OnPreFileLoaded()
{
    GEL_TextOut("MSPM0G3507: 开始文件加载前处理...\n");
    
    // 执行必要的初始化，但跳过不兼容的调试属性
    if (enableWaitForDebug) {
        GEL_TextOut("MSPM0G3507: 等待调试模式已启用\n");
    } else {
        GEL_TextOut("MSPM0G3507: 正常加载模式\n");
    }
    
    GEL_TextOut("MSPM0G3507: 文件加载前处理完成\n");
}

/*
 * OnRestart() - 重启处理函数
 * 修复: 使用兼容的目标路径名称
 */
OnRestart(int nErrorCode)
{
    GEL_TextOut("MSPM0G3507: 开始重启处理...\n");
    
    // 原版代码 (CCS Theia不兼容):
    // GEL_EvalOnTarget("CS_DAP_0", "PWRAP_DPREC0 = PWRAP_DPREC0 | 0x00020000", 0);
    
    // 修复版本 (CCS Theia兼容):
    // 方法1: 直接设置寄存器 (推荐)
    try {
        // 设置电源包装器调试寄存器
        *(int*)0x400AF000 = *(int*)0x400AF000 | 0x00020000;
        GEL_TextOut("MSPM0G3507: 电源调试寄存器设置成功\n");
    } catch (error) {
        GEL_TextOut("MSPM0G3507: 警告 - 无法设置电源调试寄存器\n");
    }
    
    // 方法2: 尝试使用新的目标路径名称
    try {
        // CCS Theia可能使用的新路径名称
        GEL_EvalOnTarget("CORTEX_M0P_0", "PWRAP_DPREC0 = PWRAP_DPREC0 | 0x00020000", 0);
        GEL_TextOut("MSPM0G3507: 使用新目标路径设置成功\n");
    } catch (error) {
        // 如果新路径也不工作，则跳过此设置
        GEL_TextOut("MSPM0G3507: 跳过目标特定的寄存器设置\n");
    }
    
    GEL_TextOut("MSPM0G3507: 重启处理完成\n");
}

/*
 * OnTargetConnect() - 目标连接后的处理
 */
OnTargetConnect()
{
    GEL_TextOut("MSPM0G3507: 目标连接成功\n");
    
    // 执行必要的初始化
    InitializeTarget();
    
    GEL_TextOut("MSPM0G3507: 目标初始化完成\n");
}

/*
 * InitializeTarget() - 目标初始化函数
 */
InitializeTarget()
{
    GEL_TextOut("MSPM0G3507: 开始目标初始化...\n");
    
    // 基本的MCU初始化
    // 1. 时钟系统初始化
    InitializeClock();
    
    // 2. 调试接口初始化
    InitializeDebugInterface();
    
    // 3. 内存系统初始化
    InitializeMemory();
    
    GEL_TextOut("MSPM0G3507: 目标初始化完成\n");
}

/*
 * InitializeClock() - 时钟系统初始化
 */
InitializeClock()
{
    GEL_TextOut("MSPM0G3507: 初始化时钟系统...\n");
    
    // MSPM0G3507时钟配置
    // 这些寄存器地址基于MSPM0G3507数据手册
    
    try {
        // 启用高速内部振荡器 (HSCLK)
        *(int*)0x400AF108 = 0x00000001;  // CLKCFG.HSCLKEN = 1
        
        // 设置系统时钟分频
        *(int*)0x400AF10C = 0x00000000;  // CLKCFG.SYSDIV = 0 (80MHz)
        
        GEL_TextOut("MSPM0G3507: 时钟系统初始化成功\n");
    } catch (error) {
        GEL_TextOut("MSPM0G3507: 警告 - 时钟初始化失败\n");
    }
}

/*
 * InitializeDebugInterface() - 调试接口初始化
 */
InitializeDebugInterface()
{
    GEL_TextOut("MSPM0G3507: 初始化调试接口...\n");
    
    try {
        // 启用调试模式
        *(int*)0xE000EDF0 = 0xA05F0001;  // CoreDebug.DHCSR
        
        // 启用DWT (Data Watchpoint and Trace)
        *(int*)0xE0001000 = 0x40000001;  // DWT.CTRL
        
        GEL_TextOut("MSPM0G3507: 调试接口初始化成功\n");
    } catch (error) {
        GEL_TextOut("MSPM0G3507: 警告 - 调试接口初始化失败\n");
    }
}

/*
 * InitializeMemory() - 内存系统初始化
 */
InitializeMemory()
{
    GEL_TextOut("MSPM0G3507: 初始化内存系统...\n");
    
    try {
        // Flash控制器初始化
        *(int*)0x400CD000 = 0x00000000;  // FLASHCTL.CMDCTL = 0
        
        // SRAM初始化 (如果需要)
        // MSPM0G3507的SRAM通常不需要特殊初始化
        
        GEL_TextOut("MSPM0G3507: 内存系统初始化成功\n");
    } catch (error) {
        GEL_TextOut("MSPM0G3507: 警告 - 内存初始化失败\n");
    }
}

/*
 * OnHalt() - 程序暂停时的处理
 */
OnHalt()
{
    GEL_TextOut("MSPM0G3507: 程序已暂停\n");
}

/*
 * OnReset() - 复位处理
 */
OnReset()
{
    GEL_TextOut("MSPM0G3507: 执行复位操作\n");
    
    // 重新初始化目标
    InitializeTarget();
}

/*
 * 辅助函数: 显示MCU信息
 */
ShowMCUInfo()
{
    GEL_TextOut("=== MSPM0G3507 MCU信息 ===\n");
    GEL_TextOut("架构: ARM Cortex-M0+\n");
    GEL_TextOut("主频: 80MHz\n");
    GEL_TextOut("Flash: 128KB\n");
    GEL_TextOut("SRAM: 32KB\n");
    GEL_TextOut("封装: LQFP-64\n");
    GEL_TextOut("========================\n");
}

/*
 * 辅助函数: 显示调试状态
 */
ShowDebugStatus()
{
    GEL_TextOut("=== 调试状态信息 ===\n");
    
    try {
        int dhcsr = *(int*)0xE000EDF0;
        GEL_TextOut("DHCSR: 0x%08X\n", dhcsr);
        
        if (dhcsr & 0x00000001) {
            GEL_TextOut("调试模式: 已启用\n");
        } else {
            GEL_TextOut("调试模式: 未启用\n");
        }
        
        if (dhcsr & 0x00020000) {
            GEL_TextOut("CPU状态: 暂停\n");
        } else {
            GEL_TextOut("CPU状态: 运行\n");
        }
    } catch (error) {
        GEL_TextOut("无法读取调试状态\n");
    }
    
    GEL_TextOut("==================\n");
}

// 菜单项定义 (CCS Theia兼容)
menuitem "MSPM0G3507 工具"
{
    hotmenu ShowMCUInfo()     { ShowMCUInfo(); }
    hotmenu ShowDebugStatus() { ShowDebugStatus(); }
    hotmenu InitializeTarget() { InitializeTarget(); }
}

GEL_TextOut("MSPM0G3507 GEL脚本加载完成 (CCS Theia兼容版本)\n");
