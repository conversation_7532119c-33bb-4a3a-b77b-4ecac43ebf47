################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
build-1655508785: ../empty.syscfg
	@echo 'Building file: "$<"'
	@echo 'Invoking: SysConfig'
	"D:/ti/ccs1281/ccs/utils/sysconfig_1.21.0/sysconfig_cli.bat" --script "C:/code/TI_CAR/empty.syscfg" -o "." -s "D:/ti/CCS-SDK/mspm0_sdk_2_05_01_00/.metadata/product.json" --compiler ticlang
	@echo 'Finished building: "$<"'
	@echo ' '

device_linker.cmd: build-1655508785 ../empty.syscfg
device.opt: build-1655508785
device.cmd.genlibs: build-1655508785
ti_msp_dl_config.c: build-1655508785
ti_msp_dl_config.h: build-1655508785
Event.dot: build-1655508785

%.o: ./%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/code/TI_CAR" -I"C:/code/TI_CAR/Debug" -I"D:/ti/CCS-SDK/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include" -I"D:/ti/CCS-SDK/mspm0_sdk_2_05_01_00/source" -I"C:/code/TI_CAR/BSP/Inc" -I"C:/code/TI_CAR/APP/Inc" -I"C:/code/TI_CAR/DMP" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

startup_mspm0g350x_ticlang.o: D:/ti/CCS-SDK/mspm0_sdk_2_05_01_00/source/ti/devices/msp/m0p/startup_system_files/ticlang/startup_mspm0g350x_ticlang.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/code/TI_CAR" -I"C:/code/TI_CAR/Debug" -I"D:/ti/CCS-SDK/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include" -I"D:/ti/CCS-SDK/mspm0_sdk_2_05_01_00/source" -I"C:/code/TI_CAR/BSP/Inc" -I"C:/code/TI_CAR/APP/Inc" -I"C:/code/TI_CAR/DMP" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

%.o: ../%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/code/TI_CAR" -I"C:/code/TI_CAR/Debug" -I"D:/ti/CCS-SDK/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include" -I"D:/ti/CCS-SDK/mspm0_sdk_2_05_01_00/source" -I"C:/code/TI_CAR/BSP/Inc" -I"C:/code/TI_CAR/APP/Inc" -I"C:/code/TI_CAR/DMP" -DMPU6050 -DMOTION_DRIVER_TARGET_MSPM0 -gdwarf-3 -w -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '


