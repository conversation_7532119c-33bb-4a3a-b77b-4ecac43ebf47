# TI_CAR工程硬件移植完整方案

## 📋 移植概述

TI_CAR工程基于TI MSPM0G3507微控制器，采用分层架构设计，具备良好的可移植性。本方案提供三种移植路径：**同平台移植**、**跨平台移植**、**功能模块移植**。

## 🎯 移植目标分析

### 当前硬件配置
- **主控**: TI MSPM0G3507 (ARM Cortex-M0+, 32MHz, 128KB Flash, 32KB SRAM)
- **传感器**: MPU6050六轴姿态传感器 (I2C接口)
- **执行器**: 四轮独立电机 (PWM+GPIO控制)
- **循迹**: 8路灰度传感器 (GPIO输入)
- **显示**: OLED显示屏 (I2C接口)
- **通信**: UART调试接口
- **人机交互**: LED指示灯、按键、蜂鸣器

## 🚀 移植方案选择

### 方案A：同平台移植 (推荐新手)
**适用场景**: 使用其他MSPM0系列MCU或相同开发板
**难度等级**: ⭐⭐☆☆☆
**移植时间**: 1-2天

### 方案B：跨平台移植 (推荐有经验开发者)
**适用场景**: 移植到STM32、ESP32、Arduino等其他平台
**难度等级**: ⭐⭐⭐⭐☆
**移植时间**: 1-2周

### 方案C：功能模块移植 (推荐定制化需求)
**适用场景**: 只移植部分功能模块到现有项目
**难度等级**: ⭐⭐⭐☆☆
**移植时间**: 3-7天

## 📊 硬件依赖分析

### 核心硬件要求
```
必需硬件:
├── 主控MCU (32位, ≥32KB Flash, ≥8KB SRAM)
├── 电机驱动 (4路PWM + 4路GPIO方向控制)
├── 编码器接口 (8路GPIO中断输入)
└── 电源管理 (3.3V/5V供电)

可选硬件:
├── MPU6050姿态传感器 (I2C接口)
├── OLED显示屏 (I2C接口)
├── 循迹传感器 (8路GPIO输入)
├── 调试串口 (UART接口)
└── 状态指示 (LED + 蜂鸣器 + 按键)
```

### 引脚资源需求
```
GPIO需求: 最少20个IO口
├── 电机PWM: 4路PWM输出
├── 电机方向: 4路GPIO输出
├── 编码器: 8路GPIO中断输入
├── 循迹传感器: 8路GPIO输入
├── LED指示: 3路GPIO输出
├── 按键输入: 3路GPIO输入
└── 蜂鸣器: 1路GPIO输出

通信接口需求:
├── I2C: 2路 (MPU6050 + OLED)
├── UART: 1路 (调试通信)
└── SWD: 1路 (程序下载调试)
```

## 🔧 方案A：同平台移植详细步骤

### 步骤1：硬件适配 (30分钟)
1. **检查目标MCU兼容性**
   ```bash
   支持的MSPM0系列:
   - MSPM0G3507 (原版)
   - MSPM0G3506 (减少Flash)
   - MSPM0L1306 (低功耗版本)
   - MSPM0C1104 (简化版本)
   ```

2. **修改SysConfig配置**
   - 打开 `empty.syscfg`
   - 更改设备型号: Device → 选择目标MCU
   - 重新分配引脚: 根据新硬件调整引脚映射
   - 生成新的配置文件

### 步骤2：引脚重映射 (1小时)
1. **创建引脚映射表**
   ```c
   // 在SysConfig.h中定义新的引脚映射
   #define LED_BOARD_PIN    DL_GPIO_PIN_XX  // 根据实际硬件修改
   #define MOTOR_PWM_PIN_1  DL_GPIO_PIN_XX  // 电机PWM引脚
   // ... 其他引脚定义
   ```

2. **验证引脚冲突**
   - 检查引脚复用功能
   - 确保PWM、I2C、UART引脚不冲突
   - 验证中断引脚可用性

### 步骤3：编译测试 (30分钟)
1. **清理重建项目**
   ```bash
   Project → Clean → Clean all projects
   Project → Build Project
   ```

2. **解决编译错误**
   - 检查引脚定义错误
   - 修复SysConfig生成的配置冲突
   - 验证库文件兼容性

### 步骤4：功能验证 (2小时)
1. **分模块测试**
   ```c
   // 测试顺序建议
   1. LED闪烁测试
   2. 串口通信测试  
   3. 电机PWM输出测试
   4. 传感器读取测试
   5. 完整功能测试
   ```

## 🔄 方案B：跨平台移植详细步骤

### 步骤1：平台选择与环境搭建 (1天)

#### STM32平台移植
```c
推荐芯片: STM32F103C8T6 / STM32F401CCU6
开发环境: STM32CubeIDE + HAL库
关键适配点:
- 时钟配置 (72MHz/84MHz)
- GPIO配置 (推挽输出/开漏输出)
- 定时器配置 (PWM频率匹配)
- I2C配置 (400kHz快速模式)
```

#### ESP32平台移植
```c
推荐芯片: ESP32-WROOM-32
开发环境: ESP-IDF / Arduino IDE
关键适配点:
- 双核任务分配
- WiFi功能集成
- 电压电平转换 (3.3V)
- 看门狗配置
```

#### Arduino平台移植
```c
推荐开发板: Arduino Mega 2560
开发环境: Arduino IDE
关键适配点:
- 库函数替换
- 中断处理方式
- 定时器资源分配
- 串口通信配置
```

### 步骤2：硬件抽象层重写 (3-5天)

#### 2.1 GPIO抽象层
```c
// 创建 HAL_GPIO.h
typedef struct {
    void* port;
    uint32_t pin;
} GPIO_Pin_t;

// 统一的GPIO操作接口
void HAL_GPIO_WritePin(GPIO_Pin_t* gpio, uint8_t state);
uint8_t HAL_GPIO_ReadPin(GPIO_Pin_t* gpio);
void HAL_GPIO_TogglePin(GPIO_Pin_t* gpio);
```

#### 2.2 PWM抽象层
```c
// 创建 HAL_PWM.h
typedef struct {
    void* timer;
    uint32_t channel;
    uint32_t frequency;
} PWM_Handle_t;

void HAL_PWM_Start(PWM_Handle_t* pwm);
void HAL_PWM_SetDutyCycle(PWM_Handle_t* pwm, float duty);
```

#### 2.3 I2C抽象层
```c
// 创建 HAL_I2C.h
typedef struct {
    void* instance;
    uint32_t speed;
} I2C_Handle_t;

int HAL_I2C_Write(I2C_Handle_t* hi2c, uint8_t addr, uint8_t reg, uint8_t* data, uint16_t len);
int HAL_I2C_Read(I2C_Handle_t* hi2c, uint8_t addr, uint8_t reg, uint8_t* data, uint16_t len);
```

### 步骤3：核心算法移植 (2-3天)

#### 3.1 任务调度器移植
```c
// 保持Task.c的核心逻辑，替换底层时钟函数
// 原版使用: uwTick (SysTick)
// 新平台: HAL_GetTick() 或 millis()

uint32_t Sys_GetTick(void) {
    #ifdef STM32_PLATFORM
        return HAL_GetTick();
    #elif ESP32_PLATFORM  
        return millis();
    #elif ARDUINO_PLATFORM
        return millis();
    #endif
}
```

#### 3.2 PID控制器移植
```c
// PID_IQMath.c 需要处理定点数运算
// 选项1: 移植TI IQMath库
// 选项2: 转换为浮点运算
// 选项3: 使用ARM CMSIS-DSP库

#ifdef USE_FLOATING_POINT
    typedef float _iq;
    #define _IQ(x) (x)
    #define _IQmpy(x,y) ((x)*(y))
    #define _IQdiv(x,y) ((x)/(y))
    #define _IQtoF(x) (x)
#endif
```

#### 3.3 MPU6050驱动移植
```c
// 保持DMP库不变，只需适配I2C底层函数
int mspm0_i2c_write(uint8_t slave_addr, uint8_t reg_addr, uint8_t length, uint8_t const *data) {
    #ifdef STM32_PLATFORM
        return HAL_I2C_Mem_Write(&hi2c1, slave_addr<<1, reg_addr, 1, (uint8_t*)data, length, 1000);
    #elif ESP32_PLATFORM
        // ESP32 I2C实现
    #endif
}
```

### 步骤4：功能验证与优化 (2-3天)

## 🎨 方案C：功能模块移植

### 可独立移植的模块

#### 模块1：任务调度器 (Task.c)
```c
移植难度: ⭐⭐☆☆☆
依赖: 系统时钟函数
用途: 实时任务调度管理
移植要点: 替换时钟获取函数即可
```

#### 模块2：PID控制器 (PID_IQMath.c)
```c
移植难度: ⭐⭐⭐☆☆  
依赖: IQMath库或浮点运算
用途: 电机速度闭环控制
移植要点: 数值运算库适配
```

#### 模块3：MPU6050驱动 (MPU6050.c + DMP库)
```c
移植难度: ⭐⭐⭐⭐☆
依赖: I2C通信接口
用途: 六轴姿态检测
移植要点: I2C底层函数适配
```

#### 模块4：循迹算法 (Tracker.c)
```c
移植难度: ⭐⭐☆☆☆
依赖: GPIO输入接口  
用途: 路径跟踪导航
移植要点: GPIO读取函数替换
```

## 📝 移植检查清单

### 硬件检查
- [ ] MCU性能满足要求 (主频≥16MHz, Flash≥32KB)
- [ ] GPIO数量充足 (≥20个可用IO)
- [ ] PWM通道充足 (≥4路)
- [ ] I2C接口可用 (≥1路)
- [ ] 中断资源充足 (≥8路外部中断)
- [ ] 电源供电稳定 (3.3V或5V)

### 软件检查  
- [ ] 开发环境搭建完成
- [ ] 编译工具链配置正确
- [ ] 硬件抽象层适配完成
- [ ] 关键库文件移植完成
- [ ] 引脚定义修改正确
- [ ] 时钟配置匹配硬件

### 功能检查
- [ ] 基础GPIO控制正常
- [ ] PWM输出频率正确
- [ ] I2C通信稳定
- [ ] 串口调试可用
- [ ] 中断响应及时
- [ ] 任务调度正常

## 🚨 常见问题与解决方案

### 问题1：编译错误
```c
错误: 未定义的引用 'DL_GPIO_setPins'
解决: 替换为目标平台的GPIO函数
原版: DL_GPIO_setPins(port, pin)
STM32: HAL_GPIO_WritePin(port, pin, GPIO_PIN_SET)
ESP32: digitalWrite(pin, HIGH)
```

### 问题2：PWM频率不匹配
```c
问题: 电机运行异常，PWM频率错误
解决: 重新计算定时器参数
公式: PWM频率 = 时钟频率 / (预分频 × 计数值)
建议: 保持1kHz-20kHz范围内
```

### 问题3：I2C通信失败
```c
问题: MPU6050或OLED无法通信
检查: 
1. 上拉电阻 (4.7kΩ)
2. 电压电平 (3.3V/5V匹配)
3. 时钟频率 (100kHz/400kHz)
4. 地址配置 (7位/8位地址)
```

### 问题4：中断冲突
```c
问题: 编码器中断不响应
解决:
1. 检查中断优先级设置
2. 确认中断向量表配置
3. 验证引脚中断功能
4. 检查中断服务函数注册
```

## 📚 移植资源与工具

### 开发工具
- **TI CCS**: 原版开发环境
- **STM32CubeIDE**: STM32平台开发
- **ESP-IDF**: ESP32专业开发
- **Arduino IDE**: Arduino平台开发
- **Keil MDK**: ARM Cortex-M开发

### 参考资料
- [TI MSPM0 SDK文档](https://www.ti.com/tool/MSPM0-SDK)
- [STM32 HAL库参考](https://www.st.com/en/embedded-software/stm32cube-mcu-mpu-packages.html)
- [ESP32技术参考手册](https://docs.espressif.com/projects/esp-idf/)
- [Arduino官方文档](https://www.arduino.cc/reference/)

### 调试工具
- **逻辑分析仪**: 分析数字信号时序
- **示波器**: 检查PWM波形质量
- **万用表**: 测量电压电流
- **串口调试助手**: 监控通信数据

## 🎯 移植建议

### 新手建议
1. **从简单开始**: 先移植LED闪烁等基础功能
2. **分步验证**: 每完成一个模块就进行测试
3. **保留原版**: 保持原工程作为参考
4. **记录问题**: 建立问题解决记录

### 进阶建议  
1. **性能优化**: 根据新平台特性优化算法
2. **功能扩展**: 添加WiFi、蓝牙等新功能
3. **代码重构**: 提高代码可读性和维护性
4. **文档完善**: 建立完整的移植文档

---

**移植成功标志**: 小车能够正常循迹行驶，姿态数据显示正确，所有传感器工作正常。

**技术支持**: 如遇到移植问题，可参考原工程注释或寻求技术社区帮助。
