# TI_CAR Indexing 问题解决指南

## 问题现象
CCS IDE的indexing一直在进行，无法完成，影响代码编辑和智能提示功能。

## 解决步骤

### 步骤1：立即停止indexing
1. 在CCS中按 `Ctrl+Alt+I` 打开Indexer状态
2. 如果显示正在indexing，点击"Cancel"
3. 或者：Project → C/C++ Index → Rebuild

### 步骤2：清理项目缓存
1. 关闭CCS
2. 删除以下目录：
   - `C:\code\TI_CAR\.metadata`
   - `C:\code\TI_CAR\Debug\*.d` (依赖文件)
   - `C:\code\TI_CAR\Debug\*.o` (目标文件)

### 步骤3：配置索引排除
1. 重新打开CCS
2. 右键项目 → Properties
3. C/C++ General → Indexer
4. 在"Files to index"中排除：
   - Debug目录
   - SDK路径：`D:/ti/CCS-SDK/mspm0_sdk_2_05_01_00`
   - 临时文件：`*.d`, `*.o`, `*.map`

### 步骤4：优化索引设置
1. Window → Preferences
2. C/C++ → Indexer
3. 设置：
   - ✅ Enable indexer
   - ✅ Index source files not included in the build
   - ❌ Index unused headers
   - ❌ Index all header variants
   - 设置Cache limit: 512MB

### 步骤5：重建索引
1. Project → Clean → Clean all projects
2. Project → C/C++ Index → Rebuild
3. 等待索引完成（应该很快）

## 预防措施

### 1. .gitignore配置
确保以下文件不被版本控制：
```
Debug/
*.d
*.o
*.map
.metadata/
.settings/
```

### 2. 项目配置优化
- 只包含必要的头文件路径
- 避免包含整个SDK路径
- 使用相对路径而非绝对路径

### 3. 定期清理
- 定期清理Debug目录
- 重建项目时先Clean
- 避免在项目目录下存放大文件

## 如果问题仍然存在

### 高级解决方案：
1. 创建新的工作空间
2. 重新导入项目
3. 检查磁盘空间是否充足
4. 更新CCS到最新版本

### 联系技术支持：
如果以上方法都无效，可能是CCS版本兼容性问题，建议：
1. 检查CCS版本与MSPM0 SDK兼容性
2. 查看TI官方论坛相关问题
3. 考虑降级或升级CCS版本